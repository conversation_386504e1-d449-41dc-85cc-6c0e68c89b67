import React from 'react'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Badge } from '@instructure/ui-badge'
import { Grid } from '@instructure/ui-grid'
import { IconGroupLine, IconClockLine, IconDashboardLine, IconDocumentLine } from '@instructure/ui-icons'
import type { ConsultationRequest, ConsultationStatistics } from '../types'

interface FacultyConsultationsProps {
  currentUserId: string
  pendingRequests: ConsultationRequest[]
  upcomingConsultations: ConsultationRequest[]
  statistics: ConsultationStatistics
}

const FacultyConsultations: React.FC<FacultyConsultationsProps> = ({
  currentUserId,
  pendingRequests,
  upcomingConsultations,
  statistics
}) => {
  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateTimeString
    }
  }

  const getTimeUntilConsultation = (dateTimeString: string) => {
    try {
      const consultationTime = new Date(dateTimeString)
      const now = new Date()
      const diffMs = consultationTime.getTime() - now.getTime()
      
      if (diffMs < 0) {
        return 'Past due'
      }
      
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffHours / 24)
      
      if (diffDays > 0) {
        return `In ${diffDays} day${diffDays !== 1 ? 's' : ''}`
      } else if (diffHours > 0) {
        return `In ${diffHours} hour${diffHours !== 1 ? 's' : ''}`
      } else {
        const diffMinutes = Math.floor(diffMs / (1000 * 60))
        return `In ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''}`
      }
    } catch {
      return ''
    }
  }

  const isConsultationSoon = (dateTimeString: string) => {
    try {
      const consultationTime = new Date(dateTimeString)
      const now = new Date()
      const diffMs = consultationTime.getTime() - now.getTime()
      const diffHours = diffMs / (1000 * 60 * 60)
      
      return diffHours <= 24 && diffHours > 0
    } catch {
      return false
    }
  }

  return (
    <div className="consultation-system">
      <View as="div">
        <div className="page-header">
          <p>Manage your consultation requests, time slots, and view upcoming appointments with students.</p>
        </div>

        {/* Quick Stats */}
        <Grid>
          <Grid.Row>
            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <View as="div" margin="0 0 small 0">
                  <Text size="xx-large" weight="bold" color="alert">
                    {pendingRequests.length}
                  </Text>
                </View>
                <View as="div">
                  <Text size="small" color="secondary">
                    Pending Requests
                  </Text>
                </View>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <View as="div" margin="0 0 small 0">
                  <Text size="xx-large" weight="bold" color="success">
                    {upcomingConsultations.length}
                  </Text>
                </View>
                <View as="div">
                  <Text size="small" color="secondary">
                    Upcoming
                  </Text>
                </View>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <View as="div" margin="0 0 small 0">
                  <Text size="xx-large" weight="bold" color="brand">
                    {statistics.total_consultations || 0}
                  </Text>
                </View>
                <View as="div">
                  <Text size="small" color="secondary">
                    Total Consultations
                  </Text>
                </View>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <View as="div" margin="0 0 small 0">
                  <Text size="xx-large" weight="bold" color="brand">
                    {Math.round(statistics.average_per_month || 0)}
                  </Text>
                </View>
                <View as="div">
                  <Text size="small" color="secondary">
                    Average per Month
                  </Text>
                </View>
              </View>
            </Grid.Col>
          </Grid.Row>
        </Grid>

        {/* Quick Actions */}
        <View as="div" margin="large 0 0 0">
          <Heading level="h2" margin="0 0 medium 0">
            Quick Actions
          </Heading>
          <Grid>
            <Grid.Row>
              <Grid.Col width={4}>
                <Flex
                  direction="column"
                  height="100%"
                >
                  <Flex.Item margin="0 0 small 0">
                    <Flex alignItems="center">
                      <Flex.Item>
                        <IconDashboardLine size="medium" />
                      </Flex.Item>
                      <Flex.Item>
                        <a
                          href="/consultation_requests/faculty_dashboard"
                          style={{
                            textDecoration: 'none',
                            color: '#0374B5',
                            fontSize: '14px',
                            marginBottom: '8px',
                            display: 'inline-block'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.textDecoration = 'underline'
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.textDecoration = 'none'
                          }}
                        >
                          Dashboard
                        </a>
                      </Flex.Item>
                    </Flex>
                  </Flex.Item>
                  <Flex.Item>
                    <Text size="small">
                      Review pending requests and manage upcoming consultations.
                    </Text>
                  </Flex.Item>
                </Flex>
              </Grid.Col>
              
              <Grid.Col width={4}>
                <Flex
                  direction="column"
                  height="100%"
                >
                  <Flex.Item margin="0 0 small 0">
                    <Flex alignItems="center">
                      <Flex.Item>
                        <IconClockLine size="medium" />
                      </Flex.Item>
                      <Flex.Item>
                        <a
                          href="/faculty_time_slots"
                          style={{
                            textDecoration: 'none',
                            color: '#0374B5',
                            fontSize: '14px',
                            marginBottom: '8px',
                            display: 'inline-block'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.textDecoration = 'underline'
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.textDecoration = 'none'
                          }}
                        >
                          Manage Slots
                        </a>
                      </Flex.Item>
                    </Flex>
                  </Flex.Item>
                  <Flex.Item>
                    <Text size="small">
                      Manage your available consultation time slots.
                    </Text>
                  </Flex.Item>
                </Flex>
              </Grid.Col>
              
              <Grid.Col width={4}>
                <Flex
                  direction="column"
                  height="100%"
                >
                  <Flex.Item margin="0 0 small 0">
                    <Flex alignItems="center">
                      <Flex.Item margin="0 small 0 0">
                        <IconDocumentLine size="medium" />
                      </Flex.Item>
                      <Flex.Item>
                        <a
                          href="/consultation_summaries"
                          style={{
                            textDecoration: 'none',
                            color: '#0374B5',
                            fontSize: '14px',
                            marginBottom: '8px',
                            display: 'inline-block'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.textDecoration = 'underline'
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.textDecoration = 'none'
                          }}
                        >
                          View Summaries
                        </a>
                      </Flex.Item>
                    </Flex>
                  </Flex.Item>
                  <Flex.Item>
                    <Text size="small">
                      View and manage consultation summaries and reports.
                    </Text>
                  </Flex.Item>
                </Flex>
              </Grid.Col>
            </Grid.Row>
          </Grid>
        </View>

        {/* Pending Requests */}
        {pendingRequests.length > 0 && (
          <View as="div" margin="large 0 0 0">
            <Heading level="h2" margin="0 0 medium 0">
              Pending Requests ({pendingRequests.length})
            </Heading>
            
            <View as="div">
              {pendingRequests.slice(0, 3).map(request => (
                <View
                  key={request.id}
                  as="div"
                  background="primary"
                  padding="medium"
                  borderRadius="medium"
                  borderWidth="small"
                  borderColor="warning"
                  margin="0 0 small 0"
                >
                  <Flex justifyItems="space-between" alignItems="start" id='request-item 1'>
                    <Flex.Item>
                      <Flex gap="x-small" margin="0 0 x-small 0">
                        <Flex.Item>
                          <Text weight="bold">
                            {request.student_name}
                          </Text>
                        </Flex.Item>
                        <Flex.Item>
                          <Text size="small" color="secondary" style={{ margin: '0 0 0 5' }}>
                            ({request.student_id})
                          </Text>
                        </Flex.Item>
                      </Flex>
                      <Flex gap="x-small" margin="0 0 x-small 0">
                        <Flex.Item>
                          <IconClockLine size="x-small" />
                        </Flex.Item>
                        <Flex.Item>
                          <Text size="small" style={{ margin: '0 0 0 5' }}>
                            {formatDateTime(request.preferred_datetime)}
                          </Text>
                        </Flex.Item>
                      </Flex>
                      <Text size="small" color="secondary">
                        {request.concern_type_display}
                      </Text>
                    </Flex.Item>
                    <Flex.Item>
                      <Button
                        size="small"
                        color="primary"
                        href={`/consultation_requests/faculty_dashboard?highlight=${request.id}`}
                      >
                        Review
                      </Button>
                    </Flex.Item>
                  </Flex>
                </View>
              ))}
              
              {pendingRequests.length > 3 && (
                <View as="div" textAlign="center" margin="medium 0 0 0">
                  <Button href="/consultation_requests/faculty_dashboard">
                    View All {pendingRequests.length} Pending Requests
                  </Button>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Upcoming Consultations */}
        {upcomingConsultations.length > 0 && (
          <View as="div" margin="large 0 0 0">
            <Heading level="h2" margin="0 0 medium 0">
              Upcoming Consultations ({upcomingConsultations.length})
            </Heading>
            
            <View as="div">
              {upcomingConsultations.slice(0, 3).map(consultation => (
                <View
                  key={consultation.id}
                  as="div"
                  background="primary"
                  padding="medium"
                  borderRadius="medium"
                  borderWidth="small"
                  borderColor={isConsultationSoon(consultation.preferred_datetime) ? 'warning' : 'success'}
                  margin="0 0 small 0"
                >
                  <Flex justifyItems="space-between" alignItems="start" id='request-item 2' >
                    <Flex.Item>
                      <Flex gap="x-small" margin="0 0 x-small 0">
                        <Flex.Item>
                          <Text weight="bold">
                            {consultation.student_name}
                          </Text>
                        </Flex.Item>
                        <Flex.Item>
                          <Text size="small" color="secondary" style={{ margin: '0 0 0 5' }}>
                            ({consultation.student_id})
                          </Text>
                        </Flex.Item>
                        {isConsultationSoon(consultation.preferred_datetime) && (
                          <Flex.Item>
                            <Text>Soon</Text>
                          </Flex.Item>
                        )}
                      </Flex>
                      <Flex gap="x-small" margin="0 0 x-small 0">
                        <Flex.Item>
                          <IconClockLine size="x-small" />
                        </Flex.Item>
                        <Flex.Item>
                          <Text size="small" style={{ margin: '0 0 0 5' }}>
                            {formatDateTime(consultation.preferred_datetime)}
                          </Text>
                        </Flex.Item>
                        <Flex.Item>
                          <Text size="small" color="secondary" style={{ margin: '0 0 0 5' }}>
                            ({getTimeUntilConsultation(consultation.preferred_datetime)})
                          </Text>
                        </Flex.Item>
                      </Flex>
                      <Text size="small" color="secondary">
                        {consultation.concern_type_display}
                      </Text>
                    </Flex.Item>
                    <Flex.Item>
                      <Button
                        size="small"
                        href={`/calendar?event_id=${consultation.id}`}
                        target="_blank"
                      >
                        View in Calendar
                      </Button>
                    </Flex.Item>
                  </Flex>
                </View>
              ))}
              
              {upcomingConsultations.length > 3 && (
                <View as="div" textAlign="center" margin="medium 0 0 0">
                  <Button href="/consultation_requests/faculty_dashboard">
                    View All {upcomingConsultations.length} Upcoming Consultations
                  </Button>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Help Section */}
        <View as="div" margin="large 0 0 0" background="secondary" padding="medium" borderRadius="medium">
          <Heading level="h4" margin="0 0 small 0">
            Faculty Resources
          </Heading>
          <Text>
            For guidance on conducting effective consultations or technical support with the system, 
            please refer to the faculty handbook or contact the IT Help Desk.
          </Text>
        </View>
      </View>
    </div>
  )
}

export default FacultyConsultations
