import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import SummariesList from '../SummariesList'
import type { ConsultationSummary, ConsultationRequest } from '../../types'

// Mock window.location.href
delete (window as any).location
window.location = { href: '' } as any

const mockPendingRequest: ConsultationRequest = {
  id: '1',
  student_name: '<PERSON>',
  student_id: 'STU001',
  faculty_name: 'Dr<PERSON>',
  preferred_datetime: '2024-01-15T10:00:00Z',
  formatted_datetime: 'January 15, 2024 at 10:00 AM',
  description: 'Need help with academic planning',
  nature_of_concern: 'academic',
  concern_type_display: 'Academic',
  status: 'approved',
  status_display: 'Approved',
  created_at: '2024-01-10T09:00:00Z',
  updated_at: '2024-01-10T09:00:00Z',
  can_be_approved: false,
  can_be_declined: false,
  can_be_completed: true,
  college_campus_institute: 'College of Engineering',
  department_program: 'Computer Science',
  semester: '1st Semester',
  academic_year: '2024-2025'
}

const mockSummaries: ConsultationSummary[] = []

const defaultProps = {
  summaries: mockSummaries,
  pendingRequests: [mockPendingRequest],
  loading: false,
  onRefresh: jest.fn()
}

describe('SummariesList', () => {
  beforeEach(() => {
    window.location.href = ''
  })

  it('renders pending consultation requests', () => {
    render(<SummariesList {...defaultProps} />)

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('(STU001)')).toBeInTheDocument()
    expect(screen.getByText('Need help with academic planning')).toBeInTheDocument()
  })

  it('shows Mark Complete button for completable requests', () => {
    render(<SummariesList {...defaultProps} />)

    expect(screen.getByText('Mark Complete')).toBeInTheDocument()
  })

  it('redirects to faculty edit form when Mark Complete is clicked', () => {
    render(<SummariesList {...defaultProps} />)

    const markCompleteButton = screen.getByText('Mark Complete')
    fireEvent.click(markCompleteButton)

    expect(window.location.href).toBe('/consultation_requests/1/faculty_edit')
  })

  it('does not show Mark Complete button for non-completable requests', () => {
    const nonCompletableRequest = {
      ...mockPendingRequest,
      can_be_completed: false
    }

    render(<SummariesList {...defaultProps} pendingRequests={[nonCompletableRequest]} />)

    expect(screen.queryByText('Mark Complete')).not.toBeInTheDocument()
  })
})
