import React from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Grid } from '@instructure/ui-grid'
import { ProgressBar } from '@instructure/ui-progress'
import { Badge } from '@instructure/ui-badge'
import { IconRefreshLine, IconDocumentLine, IconUserLine, IconStatsLine } from '@instructure/ui-icons'
import type { ConsultationSummary } from '../types'

interface SummariesDashboardProps {
  dashboardData: {
    statistics: any
    concern_type_breakdown: Record<string, number>
    recent_summaries: ConsultationSummary[]
    follow_up_required: ConsultationSummary[]
  } | null
  onRefresh: () => void
}

const SummariesDashboard: React.FC<SummariesDashboardProps> = ({
  dashboardData,
  onRefresh
}) => {
  if (!dashboardData) {
    return (
      <View as="div" textAlign="center" padding="large">
        <Text>Loading dashboard data...</Text>
      </View>
    )
  }

  const {
    statistics,
    concern_type_breakdown,
    recent_summaries,
    follow_up_required
  } = dashboardData

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  const concernTypes = Object.keys(concern_type_breakdown)
  const maxConcernCount = Math.max(...Object.values(concern_type_breakdown), 1)
  const totalConsultations = Object.values(concern_type_breakdown).reduce((sum, count) => sum + count, 0)

  return (
    <View as="div">
      <View as="div" display="flex" margin="0 0 large 0">
        <Heading level="h2" margin="0 small 0 0">
          Dashboard Overview
        </Heading>
        <Button
          renderIcon={() => <IconRefreshLine />}
          onClick={onRefresh}
        >
          Refresh
        </Button>
      </View>

      {/* Statistics Cards */}
      <Grid>
        <Grid.Row>
          <Grid.Col width={3}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center" height="100%">
              <View as="div" margin="0 0 small 0">
                <IconDocumentLine size="medium" color="brand" />
              </View>
              <View as="div" margin="0 0 small 0">
                <Text size="xx-large" weight="bold" color="brand">
                  {statistics?.total_consultations || 0}
                </Text>
              </View>
              <View as="div" margin="0 0 small 0">
                <Text size="small" color="secondary">
                  Total Consultations
                </Text>
              </View>
            </View>
          </Grid.Col>

          <Grid.Col width={3}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center" height="100%">
              <View as="div" margin="0 0 small 0">
                <IconUserLine size="medium" color="success" />
              </View>
              <View as="div" margin="0 0 small 0">
                 <Text size="xx-large" weight="bold" color="success">
                  {statistics?.with_referrals || 0}
                </Text>
              </View>
              <View as="div" margin="0 0 small 0">
                <Text size="small" color="secondary">
                  With Referrals
                </Text>
              </View>
            </View>
          </Grid.Col>

          <Grid.Col width={3}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center" height="100%">
              <View as="div" margin="0 0 small 0">
                <IconStatsLine size="medium" color="warning" />
              </View>
              <View as="div" margin="0 0 small 0">
                <Text size="xx-large" weight="bold" color="alert">
                  {statistics?.requiring_follow_up || 0}
                </Text>
              </View>
              <View as="div" margin="0 0 small 0">
                <Text size="small" color="secondary">
                  Need Follow-up
                </Text>
              </View>
            </View>
          </Grid.Col>

          <Grid.Col width={3}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center" height="100%">
              <View as="div" margin="0 0 small 0">
                <IconStatsLine size="medium" color="brand" />
              </View>
              <View as="div" margin="0 0 small 0">
                <Text size="xx-large" weight="bold" color="brand">
                  {Math.round(statistics?.average_per_month || 0)}
                </Text>
              </View>
              <View as="div" margin="0 0 small 0">
                <Text size="small" color="secondary">
                  Average per Month
                </Text>
              </View>
            </View>
          </Grid.Col>
        </Grid.Row>
      </Grid>

      {/* Concern Types Breakdown */}
      {concernTypes.length > 0 && (
        <View as="div" margin="large 0 0 0">
          <Heading level="h3" margin="0 0 medium 0">
            Consultations by Concern Type
          </Heading>
          
          <View as="div" background="primary" padding="medium" borderRadius="medium">
            <Grid>
              <Grid.Row>
                {concernTypes.map(concernType => {
                  const count = concern_type_breakdown[concernType]
                  const percentage = totalConsultations > 0 ? (count / totalConsultations) * 100 : 0
                  const progressPercentage = maxConcernCount > 0 ? (count / maxConcernCount) * 100 : 0
                  
                  return (
                    <Grid.Col key={concernType} width={concernTypes.length <= 3 ? 4 : 6}>
                      <View as="div" margin="0 0 medium 0">
                        <View as="div" display="flex" margin="0 0 x-small 0">
                          <Text size="small" weight="bold">
                            {concernType}
                          </Text>
                          <Text size="small" color="secondary">
                            {count} ({Math.round(percentage)}%)
                          </Text>
                        </View>
                        <ProgressBar
                          screenReaderLabel={`${concernType}: ${count} consultations`}
                          valueNow={progressPercentage}
                          valueMax={100}
                          size="small"
                          variant="brand"
                        />
                      </View>
                    </Grid.Col>
                  )
                })}
              </Grid.Row>
            </Grid>
          </View>
        </View>
      )}

      <Grid>
        <Grid.Row>
          {/* Recent Summaries */}
          <Grid.Col width={6}>
            <View as="div" margin="large 0 0 0">
              <Heading level="h3" margin="0 0 medium 0">
                Recent Consultations
              </Heading>
              
              <View as="div" background="primary" padding="medium" borderRadius="medium">
                {recent_summaries.length > 0 ? (
                  recent_summaries.map(summary => (
                    <View
                      key={summary.id}
                      as="div"
                      padding="small"
                      borderRadius="small"
                      borderWidth="small"
                      borderColor="brand"
                      margin="0 0 small 0"
                      background="secondary"
                    >
                      <View as="div" display="flex">
                        <View as="div">
                          <Text size="small" weight="bold">
                            {summary.student_name}
                          </Text>
                          <Text size="x-small" color="secondary">
                            {formatDate(summary.consultation_date)}
                          </Text>
                        </View>
                        <Text size="x-small" color="secondary">
                            {summary.concern_type_display || summary.concern_type || 'Unknown'}
                          </Text>
                      </View>
                    </View>
                  ))
                ) : (
                  <Text size="small" color="secondary">
                    No recent consultations
                  </Text>
                )}
              </View>
            </View>
          </Grid.Col>

          {/* Follow-up Required */}
          <Grid.Col width={6}>
            <View as="div" margin="large 0 0 0">
              <Heading level="h3" margin="0 0 medium 0">
                Requiring Follow-up
              </Heading>
              
              <View as="div" background="primary" padding="medium" borderRadius="medium">
                {follow_up_required.length > 0 ? (
                  follow_up_required.map(summary => (
                    <View
                      key={summary.id}
                      as="div"
                      padding="small"
                      borderRadius="small"
                      borderWidth="small"
                      borderColor="warning"
                      margin="0 0 small 0"
                      background="warning"
                    >
                      <View as="div" display="flex">
                        <View as="div">
                          <Text size="small" weight="bold">
                            {summary.student_name}
                          </Text>
                          <Text size="x-small">
                            {formatDate(summary.consultation_date)}
                          </Text>
                          {summary.follow_up_required && (
                            <Text size="x-small">
                              {summary.follow_up_required.substring(0, 50)}
                              {summary.follow_up_required.length > 50 ? '...' : ''}
                            </Text>
                          )}
                        </View>
                         <Text size="x-small">
                            {summary.concern_type_display || summary.concern_type || 'Unknown'}
                          </Text>
                      </View>
                    </View>
                  ))
                ) : (
                  <Text size="small" color="secondary">
                    No follow-ups required
                  </Text>
                )}
              </View>
            </View>
          </Grid.Col>
        </Grid.Row>
      </Grid>
    </View>
  )
}

export default SummariesDashboard
