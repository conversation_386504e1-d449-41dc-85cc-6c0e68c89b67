import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { TextInput } from '@instructure/ui-text-input'
import { TextArea } from '@instructure/ui-text-area'
import { FormFieldGroup } from '@instructure/ui-form-field'
import { Checkbox } from '@instructure/ui-checkbox'
import { Flex } from '@instructure/ui-flex'
import { IconUserLine, IconCalendarMonthLine } from '@instructure/ui-icons'
import { updateConsultationRequest, completeConsultationRequest } from '../services/consultationRequestsApi'
import type { ConsultationRequest } from '../types'

interface FacultyConsultationEditProps {
  currentUserId: string
  consultationRequest: ConsultationRequest & {
    student_name: string
    student_id: string
    faculty_name: string
    formatted_datetime: string
    concern_type_display: string
  }
}

interface FormData {
  place_of_consultation: string
  intervention_given: string
  referral_made: string
  students_adviser_agreement: boolean
  prepared_by_name: string
  prepared_by_designation: string
  noted_by_program_chair: string
  noted_by_college_dean: string
  conformance_signature: string
}

interface FormErrors {
  [key: string]: string
}

const FacultyConsultationEdit: React.FC<FacultyConsultationEditProps> = ({
  currentUserId,
  consultationRequest
}) => {
  const [formData, setFormData] = useState<FormData>({
    place_of_consultation: consultationRequest.place_of_consultation || '',
    intervention_given: consultationRequest.intervention_given || '',
    referral_made: consultationRequest.referral_made || '',
    students_adviser_agreement: consultationRequest.students_adviser_agreement || false,
    prepared_by_name: consultationRequest.prepared_by_name || '',
    prepared_by_designation: consultationRequest.prepared_by_designation || '',
    noted_by_program_chair: consultationRequest.noted_by_program_chair || '',
    noted_by_college_dean: consultationRequest.noted_by_college_dean || '',
    conformance_signature: consultationRequest.conformance_signature || ''
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.place_of_consultation.trim()) {
      newErrors.place_of_consultation = 'Place of consultation is required'
    }

    if (!formData.intervention_given.trim()) {
      newErrors.intervention_given = 'Intervention given is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      // First update the consultation request with the faculty-filled data
      const updateResult = await updateConsultationRequest(consultationRequest.id, formData)
      
      if (updateResult.hasError) {
        throw new Error(updateResult.errorMessage || 'Failed to update consultation request')
      }

      // Then complete the consultation
      const completeResult = await completeConsultationRequest(consultationRequest.id)
      
      if (completeResult.hasError) {
        throw new Error(completeResult.errorMessage || 'Failed to complete consultation')
      }

      // Redirect to consultation summaries
      window.location.href = '/consultation_summaries'
    } catch (error: any) {
      console.error('Error completing consultation:', error)
      if (error.response?.errors) {
        setErrors({ general: error.response.errors.join(', ') })
      } else {
        setErrors({ general: error.message || 'An error occurred while completing the consultation' })
      }
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateString
    }
  }

  return (
    <div className="consultation-system">
      <View as="div">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconUserLine /> Complete Consultation
          </Heading>
          <Text>
            Fill in the consultation details below and complete the consultation session.
          </Text>
        </div>

        {/* Student and Consultation Info (Read-only) */}
        <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 large 0">
          <Heading level="h3" margin="0 0 medium 0">
            Consultation Information
          </Heading>
          
          <Flex gap="large">
            <Flex.Item shouldGrow>
              <View as="div" margin="0 0 small 0">
                <Text weight="bold">Student:</Text> {consultationRequest.student_name} ({consultationRequest.student_id})
              </View>
              <View as="div" margin="0 0 small 0">
                <Text weight="bold">Faculty:</Text> {consultationRequest.faculty_name}
              </View>
              <View as="div" margin="0 0 small 0">
                <Text weight="bold">Date & Time:</Text> {formatDate(consultationRequest.preferred_datetime)}
              </View>
            </Flex.Item>
            
            <Flex.Item shouldGrow>
              <View as="div" margin="0 0 small 0">
                <Text weight="bold">Concern Type:</Text> {consultationRequest.concern_type_display}
              </View>
              <View as="div" margin="0 0 small 0">
                <Text weight="bold">Department:</Text> {consultationRequest.department_program}
              </View>
              <View as="div" margin="0 0 small 0">
                <Text weight="bold">Semester:</Text> {consultationRequest.semester} - {consultationRequest.academic_year}
              </View>
            </Flex.Item>
          </Flex>

          <View as="div" margin="medium 0 0 0">
            <Text weight="bold">Student's Concern:</Text>
            <View as="div" background="primary" padding="small" borderRadius="small" margin="x-small 0 0 0">
              <Text>{consultationRequest.description}</Text>
            </View>
          </View>
        </View>

        {/* Faculty Completion Form */}
        <form onSubmit={handleSubmit}>
          <FormFieldGroup description="Faculty Consultation Details" layout="stacked">
            {errors.general && (
              <View as="div" background="danger" padding="small" borderRadius="small" margin="0 0 medium 0">
                <Text color="danger">{errors.general}</Text>
              </View>
            )}

            <TextInput
              renderLabel="Place of Consultation *"
              placeholder="Where will the consultation take place?"
              value={formData.place_of_consultation}
              onChange={(e) => handleInputChange('place_of_consultation', e.target.value)}
              messages={errors.place_of_consultation ? [{ text: errors.place_of_consultation, type: 'error' }] : []}
            />

            <TextArea
              label="Consultation Outcome (To be filled by faculty) *"
              placeholder="Interventions or recommendations provided during consultation (filled by faculty)"
              value={formData.intervention_given}
              onChange={(e) => handleInputChange('intervention_given', e.target.value)}
              height="6rem"
              messages={errors.intervention_given ? [{ text: errors.intervention_given, type: 'error' }] : []}
            />

            <TextArea
              label="Referral Made"
              placeholder="Any referrals made to other services or professionals (filled by faculty)"
              value={formData.referral_made}
              onChange={(e) => handleInputChange('referral_made', e.target.value)}
              height="4rem"
            />

            <Checkbox
              label="Student's Adviser Agreement"
              checked={formData.students_adviser_agreement}
              onChange={(e) => handleInputChange('students_adviser_agreement', e.target.checked)}
            />

            <Flex gap="medium">
              <Flex.Item shouldGrow>
                <TextInput
                  renderLabel="Prepared by (Name)"
                  placeholder="Faculty member name"
                  value={formData.prepared_by_name}
                  onChange={(e) => handleInputChange('prepared_by_name', e.target.value)}
                />
              </Flex.Item>

              <Flex.Item shouldGrow>
                <TextInput
                  renderLabel="Designation"
                  placeholder="Faculty designation/title"
                  value={formData.prepared_by_designation}
                  onChange={(e) => handleInputChange('prepared_by_designation', e.target.value)}
                />
              </Flex.Item>
            </Flex>

            <Flex gap="medium">
              <Flex.Item shouldGrow>
                <TextInput
                  renderLabel="Noted by Program Chair"
                  placeholder="Program chair name"
                  value={formData.noted_by_program_chair}
                  onChange={(e) => handleInputChange('noted_by_program_chair', e.target.value)}
                />
              </Flex.Item>

              <Flex.Item shouldGrow>
                <TextInput
                  renderLabel="Noted by College Dean"
                  placeholder="College dean name"
                  value={formData.noted_by_college_dean}
                  onChange={(e) => handleInputChange('noted_by_college_dean', e.target.value)}
                />
              </Flex.Item>
            </Flex>

            <TextInput
              renderLabel="Conformance Signature"
              placeholder="Digital signature or confirmation"
              value={formData.conformance_signature}
              onChange={(e) => handleInputChange('conformance_signature', e.target.value)}
            />
          </FormFieldGroup>

          <View as="div" margin="large 0 0 0">
            <Flex justifyItems="end" gap="small">
              <Flex.Item>
                <Button
                  href="/consultation_summaries"
                  disabled={loading}
                >
                  Cancel
                </Button>
              </Flex.Item>
              <Flex.Item>
                <Button
                  type="submit"
                  color="primary"
                  disabled={loading}
                >
                  {loading ? 'Completing Consultation...' : 'Complete Consultation'}
                </Button>
              </Flex.Item>
            </Flex>
          </View>
        </form>
      </View>
    </div>
  )
}

export default FacultyConsultationEdit
