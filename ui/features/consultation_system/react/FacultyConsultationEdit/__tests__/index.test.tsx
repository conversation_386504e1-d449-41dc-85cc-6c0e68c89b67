import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import FacultyConsultationEdit from '../index'
import { updateConsultationRequest, completeConsultationRequest } from '../../services/consultationRequestsApi'
import type { ConsultationRequest } from '../../types'

// Mock the API services
jest.mock('../../services/consultationRequestsApi', () => ({
  updateConsultationRequest: jest.fn(),
  completeConsultationRequest: jest.fn()
}))

// Mock window.location.href
delete (window as any).location
window.location = { href: '' } as any

const mockUpdateConsultationRequest = updateConsultationRequest as jest.MockedFunction<typeof updateConsultationRequest>
const mockCompleteConsultationRequest = completeConsultationRequest as jest.MockedFunction<typeof completeConsultationRequest>

const mockConsultationRequest: ConsultationRequest & {
  student_name: string
  student_id: string
  faculty_name: string
  formatted_datetime: string
  concern_type_display: string
} = {
  id: '5',
  student_name: '<PERSON>',
  student_id: 'STU001',
  faculty_name: 'Dr<PERSON>',
  preferred_datetime: '2024-01-15T10:00:00Z',
  formatted_datetime: 'January 15, 2024 at 10:00 AM',
  description: 'Need help with academic planning',
  nature_of_concern: 'academic',
  concern_type_display: 'Academic',
  status: 'approved',
  status_display: 'Approved',
  created_at: '2024-01-10T09:00:00Z',
  updated_at: '2024-01-10T09:00:00Z',
  can_be_approved: false,
  can_be_declined: false,
  can_be_completed: true,
  college_campus_institute: 'College of Engineering',
  department_program: 'Computer Science',
  semester: '1st Semester',
  academic_year: '2024-2025',
  place_of_consultation: '',
  intervention_given: '',
  referral_made: '',
  students_adviser_agreement: false,
  prepared_by_name: '',
  prepared_by_designation: '',
  noted_by_program_chair: '',
  noted_by_college_dean: '',
  conformance_signature: ''
}

const defaultProps = {
  currentUserId: '53',
  consultationRequest: mockConsultationRequest
}

describe('FacultyConsultationEdit', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    window.location.href = ''
  })

  it('renders consultation information correctly', () => {
    render(<FacultyConsultationEdit {...defaultProps} />)
    
    expect(screen.getByText('Complete Consultation')).toBeInTheDocument()
    expect(screen.getByText('John Doe (STU001)')).toBeInTheDocument()
    expect(screen.getByText('Dr. Smith')).toBeInTheDocument()
    expect(screen.getByText('Academic')).toBeInTheDocument()
    expect(screen.getByText('Need help with academic planning')).toBeInTheDocument()
  })

  it('renders all required form fields', () => {
    render(<FacultyConsultationEdit {...defaultProps} />)
    
    expect(screen.getByLabelText(/Place of Consultation/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Consultation Outcome/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Referral Made/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Student's Adviser Agreement/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Prepared by \(Name\)/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Designation/)).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    render(<FacultyConsultationEdit {...defaultProps} />)
    
    const submitButton = screen.getByText('Complete Consultation')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Place of consultation is required')).toBeInTheDocument()
      expect(screen.getByText('Intervention given is required')).toBeInTheDocument()
    })

    expect(mockUpdateConsultationRequest).not.toHaveBeenCalled()
  })

  it('submits form with valid data', async () => {
    mockUpdateConsultationRequest.mockResolvedValue({
      data: { ...mockConsultationRequest, status: 'completed' },
      hasError: false
    })
    
    mockCompleteConsultationRequest.mockResolvedValue({
      data: { ...mockConsultationRequest, status: 'completed' },
      hasError: false
    })

    render(<FacultyConsultationEdit {...defaultProps} />)
    
    // Fill in required fields
    const placeInput = screen.getByLabelText(/Place of Consultation/)
    const interventionTextarea = screen.getByLabelText(/Consultation Outcome/)
    
    fireEvent.change(placeInput, { target: { value: 'Faculty Office' } })
    fireEvent.change(interventionTextarea, { target: { value: 'Provided academic guidance' } })

    const submitButton = screen.getByText('Complete Consultation')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockUpdateConsultationRequest).toHaveBeenCalledWith('5', {
        place_of_consultation: 'Faculty Office',
        intervention_given: 'Provided academic guidance',
        referral_made: '',
        students_adviser_agreement: false,
        prepared_by_name: '',
        prepared_by_designation: '',
        noted_by_program_chair: '',
        noted_by_college_dean: '',
        conformance_signature: ''
      })
    })

    await waitFor(() => {
      expect(mockCompleteConsultationRequest).toHaveBeenCalledWith('5')
    })

    await waitFor(() => {
      expect(window.location.href).toBe('/consultation_summaries')
    })
  })

  it('handles API errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
    
    mockUpdateConsultationRequest.mockResolvedValue({
      data: null,
      hasError: true,
      errorMessage: 'Failed to update consultation'
    })

    render(<FacultyConsultationEdit {...defaultProps} />)
    
    // Fill in required fields
    const placeInput = screen.getByLabelText(/Place of Consultation/)
    const interventionTextarea = screen.getByLabelText(/Consultation Outcome/)
    
    fireEvent.change(placeInput, { target: { value: 'Faculty Office' } })
    fireEvent.change(interventionTextarea, { target: { value: 'Provided academic guidance' } })

    const submitButton = screen.getByText('Complete Consultation')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Failed to update consultation')).toBeInTheDocument()
    })

    expect(window.location.href).toBe('')
    
    consoleSpy.mockRestore()
  })

  it('shows loading state during submission', async () => {
    mockUpdateConsultationRequest.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

    render(<FacultyConsultationEdit {...defaultProps} />)
    
    // Fill in required fields
    const placeInput = screen.getByLabelText(/Place of Consultation/)
    const interventionTextarea = screen.getByLabelText(/Consultation Outcome/)
    
    fireEvent.change(placeInput, { target: { value: 'Faculty Office' } })
    fireEvent.change(interventionTextarea, { target: { value: 'Provided academic guidance' } })

    const submitButton = screen.getByText('Complete Consultation')
    fireEvent.click(submitButton)

    expect(screen.getByText('Completing Consultation...')).toBeInTheDocument()
    expect(submitButton).toBeDisabled()
  })
})
