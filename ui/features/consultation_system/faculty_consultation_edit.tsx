import React from 'react'
import { createRoot } from 'react-dom/client'
import FacultyConsultationEdit from './react/FacultyConsultationEdit'

// Initialize the Faculty Consultation Edit Form
const initializeFacultyConsultationEdit = () => {
  const container = document.getElementById('faculty-consultation-edit-container')
  if (!container) {
    console.error('Faculty consultation edit container not found')
    return
  }

  const envData = window.ENV?.FACULTY_CONSULTATION_EDIT
  if (!envData) {
    console.error('Faculty consultation edit environment data not found')
    return
  }

  const root = createRoot(container)
  root.render(
    <FacultyConsultationEdit
      currentUserId={envData.current_user_id}
      consultationRequest={envData.consultation_request}
    />
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeFacultyConsultationEdit)
} else {
  initializeFacultyConsultationEdit()
}

export default initializeFacultyConsultationEdit
